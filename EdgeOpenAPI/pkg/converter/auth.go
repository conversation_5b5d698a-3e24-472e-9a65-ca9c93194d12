package converter

import (
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
)

// LoginRequest represents the JSON structure for user login
type LoginRequest struct {
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	CSRFToken string `json:"csrfToken,omitempty"` // CSRF token for security
	Remember  bool   `json:"remember,omitempty"`  // Remember login option
}

// LoginResponse represents the JSON structure for login response
type LoginResponse struct {
	Success     bool   `json:"success"`
	Message     string `json:"message,omitempty"`
	UserID      int64  `json:"user_id,omitempty"`
	AccessToken string `json:"access_token,omitempty"`
	ExpiresAt   int64  `json:"expires_at,omitempty"`
	TokenType   string `json:"token_type,omitempty"`
}

// AccessKeyInfo represents user access key information
type AccessKeyInfo struct {
	ID          int64     `json:"id"`
	UniqueID    string    `json:"unique_id"`
	Secret      string    `json:"secret"`
	Description string    `json:"description"`
	IsOn        bool      `json:"is_on"`
	AccessedAt  time.Time `json:"accessed_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// CreateAccessKeyRequest represents the request to create an access key
type CreateAccessKeyRequest struct {
	Description string `json:"description"`
}

// CreateAccessKeyResponse represents the response when creating an access key
type CreateAccessKeyResponse struct {
	AccessKeyID int64  `json:"access_key_id"`
	UniqueID    string `json:"unique_id"`
	Secret      string `json:"secret"`
	Message     string `json:"message"`
}

// LoginUserToProto converts LoginRequest to protobuf LoginUserRequest
func LoginUserToProto(req *LoginRequest) *pb.LoginUserRequest {
	return &pb.LoginUserRequest{
		Username: req.Username,
		Password: req.Password,
	}
}

// LoginResponseFromProto converts protobuf LoginUserResponse to JSON LoginResponse
func LoginResponseFromProto(pbResp *pb.LoginUserResponse) *LoginResponse {
	return &LoginResponse{
		Success: pbResp.IsOk,
		Message: pbResp.Message,
		UserID:  pbResp.UserId,
	}
}

// AccessKeyFromProto converts protobuf UserAccessKey to JSON AccessKeyInfo
func AccessKeyFromProto(pbKey *pb.UserAccessKey) *AccessKeyInfo {
	if pbKey == nil {
		return nil
	}

	return &AccessKeyInfo{
		ID:          pbKey.Id,
		UniqueID:    pbKey.UniqueId,
		Secret:      pbKey.Secret,
		Description: pbKey.Description,
		IsOn:        pbKey.IsOn,
		AccessedAt:  time.Unix(pbKey.AccessedAt, 0),
		CreatedAt:   time.Unix(pbKey.AccessedAt, 0), // Use AccessedAt as CreatedAt since CreatedAt is not available
	}
}

// AccessKeysFromProto converts a slice of protobuf UserAccessKeys to JSON AccessKeyInfos
func AccessKeysFromProto(pbKeys []*pb.UserAccessKey) []*AccessKeyInfo {
	keys := make([]*AccessKeyInfo, len(pbKeys))
	for i, pbKey := range pbKeys {
		keys[i] = AccessKeyFromProto(pbKey)
	}
	return keys
}

// CreateAccessKeyToProto converts CreateAccessKeyRequest to protobuf CreateUserAccessKeyRequest
func CreateAccessKeyToProto(userId int64, req *CreateAccessKeyRequest) *pb.CreateUserAccessKeyRequest {
	return &pb.CreateUserAccessKeyRequest{
		UserId:      userId,
		Description: req.Description,
	}
}
