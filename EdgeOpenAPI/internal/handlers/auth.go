package handlers

import (
	"context"
	"net/http"
	"strconv"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
)

// <PERSON>th<PERSON>and<PERSON> handles authentication-related HTTP requests
type AuthHandler struct {
	edgeAPIClient *grpc.EdgeAPIClient
}

// NewAuthHandler creates a new authentication handler
func <PERSON>AuthHandler(edgeAPIClient *grpc.EdgeAPIClient) *AuthHandler {
	return &AuthHandler{
		edgeAPIClient: edgeAPIClient,
	}
}

// Login handles POST /api/v1/auth/login
func (h *AuthHandler) Login(c *gin.Context) {
	var req converter.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Validate input
	if len(req.Username) == 0 || len(req.Password) == 0 {
		converter.ValidationErrorResponse(c, gin.H{
			"username": "Username is required",
			"password": "Password is required",
		})
		return
	}

	// Create API context for gRPC call (requires API node credentials)
	ctx := h.edgeAPIClient.CreateAPIContext()

	// Convert to protobuf request
	pbReq := converter.LoginUserToProto(&req)

	// Call EdgeAPI to validate user credentials
	resp, err := h.edgeAPIClient.UserService.LoginUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert response
	loginResp := converter.LoginResponseFromProto(resp)

	if !loginResp.Success {
		// Login failed
		converter.ErrorResponse(c, http.StatusUnauthorized, "Login failed", gin.H{
			"type":    "authentication_error",
			"details": loginResp.Message,
		})
		return
	}

	// Login successful - now we need to provide access credentials
	// For now, we'll return the user ID and suggest creating access keys
	loginResp.Message = "Login successful. Please create access keys for API access."
	loginResp.TokenType = "Bearer"

	converter.SuccessResponse(c, loginResp)
}

// LoginWithCSRF handles POST /api/v1/auth/login-csrf (User login with CSRF protection)
func (h *AuthHandler) LoginWithCSRF(c *gin.Context) {
	var req converter.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Validate input
	if len(req.Username) == 0 || len(req.Password) == 0 {
		converter.ValidationErrorResponse(c, gin.H{
			"username": "Username is required",
			"password": "Password is required",
		})
		return
	}

	// Validate CSRF token
	if req.CSRFToken == "" {
		converter.ErrorResponse(c, http.StatusForbidden, "缺少CSRF token", gin.H{
			"type": "csrf_error",
			"code": "missing_token",
		})
		return
	}

	// Validate CSRF token
	if !ValidateCSRFToken(req.CSRFToken) {
		converter.ErrorResponse(c, http.StatusForbidden, "CSRF token无效或已过期，请刷新页面后重试", gin.H{
			"type": "csrf_error",
			"code": "invalid_token",
		})
		return
	}

	// Create API context for gRPC call (requires API node credentials)
	ctx := h.edgeAPIClient.CreateAPIContext()

	// Convert to protobuf request
	pbReq := converter.LoginUserToProto(&req)

	// Call EdgeAPI to validate user credentials
	resp, err := h.edgeAPIClient.UserService.LoginUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert response
	loginResp := converter.LoginResponseFromProto(resp)

	if !loginResp.Success {
		// Login failed
		converter.ErrorResponse(c, http.StatusUnauthorized, "登录失败", gin.H{
			"type":    "authentication_error",
			"details": loginResp.Message,
		})
		return
	}

	// Login successful
	loginResp.Message = "登录成功"
	loginResp.TokenType = "Bearer"

	converter.SuccessResponse(c, loginResp)
}

// CreateAccessKey handles POST /api/v1/auth/access-keys
func (h *AuthHandler) CreateAccessKey(c *gin.Context) {
	var req converter.CreateAccessKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Get user ID from authenticated context
	// Note: This requires the user to be authenticated with existing API keys
	accessToken := middleware.GetAccessTokenFromContext(c)
	if accessToken == "" {
		converter.UnauthorizedResponse(c, "Authentication required to create access keys")
		return
	}

	// For now, we'll need to get the user ID from the access token
	// This is a simplified implementation - in a real scenario, you'd decode the token
	// or make a call to get the current user info

	// TODO: Implement proper user ID extraction from access token
	// For now, we'll return an error suggesting the proper flow
	converter.ErrorResponse(c, http.StatusNotImplemented, "Access key creation not yet implemented", gin.H{
		"type":    "not_implemented_error",
		"details": "Please use the EdgeAPI admin interface to create access keys for now",
	})
}

// ListAccessKeys handles GET /api/v1/auth/access-keys
func (h *AuthHandler) ListAccessKeys(c *gin.Context) {
	// Get authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	_ = accessToken // Mark as used to avoid compiler warning

	// TODO: Get user ID from access token
	// For now, we'll return an error suggesting the proper flow
	converter.ErrorResponse(c, http.StatusNotImplemented, "Access key listing not yet implemented", gin.H{
		"type":    "not_implemented_error",
		"details": "Please use the EdgeAPI admin interface to manage access keys for now",
	})
}

// DeleteAccessKey handles DELETE /api/v1/auth/access-keys/{id}
func (h *AuthHandler) DeleteAccessKey(c *gin.Context) {
	// Parse access key ID from URL
	accessKeyIdStr := c.Param("id")
	accessKeyId, err := strconv.ParseInt(accessKeyIdStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, gin.H{
			"field":   "id",
			"message": "Invalid access key ID",
		})
		return
	}

	// Get authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Call EdgeAPI to delete access key
	_, err = h.edgeAPIClient.UserAccessKeyService.DeleteUserAccessKey(ctx, &pb.DeleteUserAccessKeyRequest{
		UserAccessKeyId: accessKeyId,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{
		"message": "Access key deleted successfully",
	})
}

// ValidateToken handles POST /api/v1/auth/validate
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// This endpoint validates the current authentication token
	accessToken := middleware.GetAccessTokenFromContext(c)
	if accessToken == "" {
		converter.UnauthorizedResponse(c, "No valid token found")
		return
	}

	// If we reach here, the token is valid (middleware already validated it)
	converter.SuccessResponse(c, gin.H{
		"valid":   true,
		"message": "Token is valid",
	})
}
