package handlers

import (
	"net/http"
	"sync"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/csrf"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// CSRFHandler 处理CSRF token相关请求
type CSRFHandler struct {
	lastTimestamp int64
	locker        sync.Mutex
}

// NewCSRFHandler 创建新的CSRF处理器
func NewCSRFHandler() *CSRFHandler {
	return &CSRFHandler{}
}

// GetToken 处理GET /csrf/token请求
func (h *CSRFHandler) GetToken(c *gin.Context) {
	h.locker.Lock()
	defer h.locker.Unlock()

	defer func() {
		h.lastTimestamp = time.Now().Unix()
	}()

	// 速率限制：未登录用户限制请求频率
	// 这里简化处理，实际应用中可以根据IP或其他标识进行限制
	if h.lastTimestamp > 0 && time.Now().Unix()-h.lastTimestamp <= 0 {
		converter.ErrorResponse(c, http.StatusTooManyRequests, "请求速度过快，请稍后重试", gin.H{
			"type": "rate_limit_error",
		})
		return
	}

	// 生成CSRF token
	token := csrf.Generate()

	converter.SuccessResponse(c, gin.H{
		"token": token,
	})
}

// ValidateToken 验证CSRF token（中间件使用）
func ValidateToken(token string) bool {
	return csrf.Validate(token)
}
