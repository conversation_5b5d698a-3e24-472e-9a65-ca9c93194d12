package csrf

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"strconv"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
)

// Generate 生成CSRF token
func Generate() string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	
	// 获取配置中的密钥
	cfg := config.GetConfig()
	secret := cfg.Security.Secret
	
	// 生成hash
	h := sha256.New()
	h.Write([]byte(secret))
	h.Write([]byte(timestamp))
	s := h.Sum(nil)
	
	// 组合token: timestamp + hash
	token := base64.StdEncoding.EncodeToString([]byte(timestamp + fmt.Sprintf("%x", s)))
	
	// 存储到管理器
	sharedTokenManager.Put(token)
	
	return token
}

// Validate 验证CSRF token
func Validate(token string) bool {
	if len(token) == 0 {
		return false
	}

	// 检查token是否在管理器中
	if !sharedTokenManager.Exists(token) {
		return false
	}
	
	// 验证后删除token（一次性使用）
	defer func() {
		sharedTokenManager.Delete(token)
	}()

	// 解码token
	data, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return false
	}

	hashString := string(data)
	if len(hashString) < 10+64 { // 10位时间戳 + 64位hash
		return false
	}

	// 提取时间戳和hash
	timestampString := hashString[:10]
	hashString = hashString[10:]

	// 重新计算hash进行验证
	cfg := config.GetConfig()
	secret := cfg.Security.Secret
	
	h := sha256.New()
	h.Write([]byte(secret))
	h.Write([]byte(timestampString))
	hashData := h.Sum(nil)
	
	if hashString != fmt.Sprintf("%x", hashData) {
		return false
	}

	// 检查时间戳有效性（30分钟有效期）
	timestamp, err := strconv.ParseInt(timestampString, 10, 64)
	if err != nil {
		return false
	}
	
	if timestamp < time.Now().Unix()-1800 { // 有效期30分钟
		return false
	}

	return true
}

// GetSharedTokenManager 获取共享的token管理器（用于测试）
func GetSharedTokenManager() *TokenManager {
	return sharedTokenManager
}
