package csrf

import (
	"sync"
	"time"
)

// TokenManager CSRF token内存管理器
type TokenManager struct {
	tokenMap map[string]int64 // token => timestamp
	locker   sync.Mutex
}

var sharedTokenManager = NewTokenManager()

// init 启动清理协程
func init() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour)
		defer ticker.Stop()
		for range ticker.C {
			sharedTokenManager.Clean()
		}
	}()
}

// NewTokenManager 创建新的token管理器
func NewTokenManager() *TokenManager {
	return &TokenManager{
		tokenMap: make(map[string]int64),
	}
}

// Put 存储token
func (tm *TokenManager) Put(token string) {
	tm.locker.Lock()
	defer tm.locker.Unlock()
	tm.tokenMap[token] = time.Now().Unix()
}

// Exists 检查token是否存在
func (tm *TokenManager) Exists(token string) bool {
	tm.locker.Lock()
	defer tm.locker.Unlock()
	_, ok := tm.tokenMap[token]
	return ok
}

// Delete 删除token
func (tm *TokenManager) Delete(token string) {
	tm.locker.Lock()
	defer tm.locker.Unlock()
	delete(tm.tokenMap, token)
}

// Clean 清理过期token
func (tm *TokenManager) Clean() {
	tm.locker.Lock()
	defer tm.locker.Unlock()
	
	now := time.Now().Unix()
	for token, timestamp := range tm.tokenMap {
		// 删除一个小时前的token
		if now-timestamp > 3600 {
			delete(tm.tokenMap, token)
		}
	}
}

// Size 获取当前token数量（用于调试）
func (tm *TokenManager) Size() int {
	tm.locker.Lock()
	defer tm.locker.Unlock()
	return len(tm.tokenMap)
}
