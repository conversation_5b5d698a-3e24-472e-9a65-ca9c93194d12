package middleware

import (
	"net/http"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/csrf"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// CSRFMiddleware CSRF token验证中间件
func CSRFMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对POST、PUT、DELETE等修改操作进行CSRF验证
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// 从请求中获取CSRF token
		token := getCSRFTokenFromRequest(c)
		if token == "" {
			converter.ErrorResponse(c, http.StatusForbidden, "缺少CSRF token", gin.H{
				"type": "csrf_error",
				"code": "missing_token",
			})
			c.Abort()
			return
		}

		// 验证CSRF token
		if !csrf.Validate(token) {
			converter.ErrorResponse(c, http.StatusForbidden, "CSRF token无效或已过期，请刷新页面后重试", gin.H{
				"type": "csrf_error",
				"code": "invalid_token",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getCSRFTokenFromRequest 从请求中获取CSRF token
func getCSRFTokenFromRequest(c *gin.Context) string {
	// 优先从Header中获取
	token := c.GetHeader("X-CSRF-Token")
	if token != "" {
		return token
	}

	// 从表单参数中获取
	token = c.PostForm("csrfToken")
	if token != "" {
		return token
	}

	// 从JSON body中获取
	var body struct {
		CSRFToken string `json:"csrfToken"`
	}
	if err := c.ShouldBindJSON(&body); err == nil && body.CSRFToken != "" {
		return body.CSRFToken
	}

	return ""
}
