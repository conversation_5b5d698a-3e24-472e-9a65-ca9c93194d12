// 认证相关API服务

// 用户信息类型
export interface UserInfo {
  accountNo: string
  email: string
  username: string
  userId: number
  role: string[]
  exp: number
}

// CSRF token响应类型
export interface CSRFTokenResponse {
  token: string
}

// 用户登录请求类型
export interface LoginRequest {
  username: string
  password: string
  csrfToken: string
  remember?: boolean
}

// 用户登录响应类型
export interface LoginResponse {
  success: boolean
  message?: string
  user_id?: number
  access_token?: string
  expires_at?: number
  token_type?: string
}

// API配置
const API_ENDPOINT = import.meta.env.VITE_EDGE_OPENAPI_ENDPOINT ||
  (import.meta.env.DEV ? 'http://localhost:8080' : 'http://127.0.0.1:8080')



// 认证服务
class AuthService {
  // 获取CSRF token
  async getCSRFToken(): Promise<CSRFTokenResponse> {
    try {
      const response = await fetch(`${API_ENDPOINT}/csrf/token`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return { token: result.data.token }
    } catch (error) {
      console.error('获取CSRF token失败:', error)
      throw error
    }
  }



  // 用户登录
  async loginUser(username: string, password: string, remember: boolean = false): Promise<LoginResponse> {
    try {
      // 先获取CSRF token
      const csrfResponse = await this.getCSRFToken()

      // 然后进行登录
      const loginRequest: LoginRequest = {
        username,
        password,
        csrfToken: csrfResponse.token,
        remember
      }

      const response = await fetch(`${API_ENDPOINT}/api/v1/auth/login-csrf`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(loginRequest)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return result.data
    } catch (error) {
      console.error('EdgeOpenAPI用户登录失败:', error)
      throw error
    }
  }

  // 创建用户信息（从登录响应）
  createUserInfo(response: LoginResponse, username: string): UserInfo {
    return {
      accountNo: response.user_id?.toString() || '',
      email: this.getUsernameType(username) === 'email' ? username : '',
      username,
      userId: response.user_id || 0,
      role: ['user'],
      exp: response.expires_at || (Date.now() + 24 * 60 * 60 * 1000) // 使用响应中的过期时间或默认24小时
    }
  }

  // 生成访问令牌
  generateAccessToken(userId: number, localSid?: string): string {
    const payload = {
      userId,
      localSid,
      exp: Date.now() + 24 * 60 * 60 * 1000
    }
    return btoa(JSON.stringify(payload))
  }

  // 获取用户名类型
  getUsernameType(username: string): 'email' | 'phone' | 'username' {
    if (username.includes('@')) {
      return 'email'
    }
    if (/^\d+$/.test(username)) {
      return 'phone'
    }
    return 'username'
  }

  // 验证用户名
  validateUsername(username: string): boolean {
    if (!username || username.length < 3) {
      return false
    }
    
    const type = this.getUsernameType(username)
    switch (type) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username)
      case 'phone':
        return /^\d{10,15}$/.test(username)
      case 'username':
        return /^[a-zA-Z0-9_-]{3,20}$/.test(username)
      default:
        return false
    }
  }
}

// 导出实例
export const authService = new AuthService()
export default authService 
